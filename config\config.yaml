# Arbitrage Bot Configuration
bot:
  name: "Crypto Arbitrage Bot"
  version: "1.0.0"
  monitoring_interval: 5 # seconds
  profit_threshold: 1.0 # minimum profit percentage
  max_signal_frequency: 60 # seconds between same pair signals

# Trading pairs to monitor
trading_pairs:
  - symbol: "ADA/USDT"
    enabled: true
    min_profit: 1.0
  - symbol: "LABUBU/USDT"
    enabled: true
    min_profit: 1.0

# Exchange configuration
exchanges:
  mexc:
    enabled: true
    rate_limit: 1200
    timeout: 10
    api_urls:
      - "https://api.mexc.com/api/v3"
      - "https://api.mexc.global"
  gate:
    enabled: true
    rate_limit: 900
    timeout: 10
    api_urls:
      - "https://api.gateio.ws"
      - "https://data.gate.io"
  lbank:
    enabled: true
    rate_limit: 600
    timeout: 10
    api_urls:
      - "https://api.lbank.info"
      - "https://api.lbank.me"
  mock_exchange_1:
    enabled: true
    type: real
  mock_exchange_2:
    enabled: true
    type: real

# Database settings
database:
  path: "data/arbitrage.db"
  backup_interval: 3600 # seconds
  cleanup_days: 30

# Logging configuration
logging:
  level: "INFO"
  file_path: "logs/arbitrage.log"
  max_file_size: "10MB"
  backup_count: 5
  console_output: true

# Telegram settings (set via environment variables)
telegram:
  enabled: true
  parse_mode: "HTML"
  disable_web_page_preview: true
  message_template: |
    🚨 <b>Arbitrage Opportunity</b> 🚨

    📊 <b>Pair:</b> {symbol}
    💰 <b>Profit:</b> {profit_percent}%
    📈 <b>Action:</b> {action}

    🏪 <b>Buy:</b> {buy_exchange} - ${buy_price}
    🏪 <b>Sell:</b> {sell_exchange} - ${sell_price}

    ⏰ <b>Time:</b> {timestamp}
    💵 <b>Spread:</b> ${price_diff}
